import { query } from '~/utils/database';
import { logger, logAdminAction, getClientIP } from '~/utils/logger';
import { checkAdminPermission, PERMISSIONS } from '~/utils/permission';

/**
 * 管理员获取新闻分类列表接口
 * GET /api/admin/news/categories
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法查看分类列表'
    //   });
    // }

    // 获取查询参数
    const query_params = getQuery(event);
    const { 
      page = 1, 
      pageSize = 50, 
      search,
      parentId,
      isActive
    } = query_params;

    // 参数验证
    const pageNum = Math.max(1, parseInt(page as string) || 1);
    const pageSizeNum = Math.min(100, Math.max(1, parseInt(pageSize as string) || 50));
    const offset = (pageNum - 1) * pageSizeNum;

    // 构建查询条件
    let whereConditions: string[] = [];
    let queryParams: any[] = [];

    // 搜索条件
    if (search && typeof search === 'string' && search.trim()) {
      whereConditions.push('(name LIKE ? OR description LIKE ?)');
      const searchTerm = `%${search.trim()}%`;
      queryParams.push(searchTerm, searchTerm);
    }

    // 父分类筛选
    if (parentId !== undefined) {
      if (parentId === 'null' || parentId === '') {
        whereConditions.push('parent_id IS NULL');
      } else if (!isNaN(parseInt(parentId as string))) {
        whereConditions.push('parent_id = ?');
        queryParams.push(parseInt(parentId as string));
      }
    }

    // 状态筛选
    if (isActive !== undefined) {
      whereConditions.push('is_active = ?');
      queryParams.push(isActive === 'true' ? 1 : 0);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // 查询分类列表（包含新闻数量统计）
    const categoriesQuery = `
      SELECT 
        nc.id,
        nc.name,
        nc.slug,
        nc.description,
        nc.parent_id,
        nc.sort_order,
        nc.is_active,
        nc.created_at,
        nc.updated_at,
        COUNT(n.id) as news_count
      FROM news_categories nc
      LEFT JOIN news n ON nc.id = n.category_id AND n.status = 'published'
      ${whereClause}
      GROUP BY nc.id, nc.name, nc.slug, nc.description, nc.parent_id, nc.sort_order, nc.is_active, nc.created_at, nc.updated_at
      ORDER BY nc.sort_order ASC, nc.id ASC
      LIMIT ${pageSizeNum} OFFSET ${offset}
    `;

    // 查询总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM news_categories nc
      ${whereClause}
    `;

    // 执行查询
    const [categories, countResult] = await Promise.all([
      query(categoriesQuery, queryParams),
      query(countQuery, queryParams)
    ]);

    const total = countResult[0]?.total || 0;
    const totalPages = Math.ceil(total / pageSizeNum);

    // 格式化分类数据
    const formattedCategories = categories.map((category: any) => ({
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      parentId: category.parent_id,
      sortOrder: category.sort_order,
      isActive: category.is_active === 1,
      newsCount: category.news_count || 0,
      createdAt: category.created_at,
      updatedAt: category.updated_at
    }));

    return {
      success: true,
      data: {
        list: formattedCategories,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total,
          totalPages
        }
      }
    };

  } catch (error: any) {
    logger.error('获取新闻分类列表失败', {
      error: error.message,
      stack: error.stack,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: '获取分类列表失败'
    });
  }
});
