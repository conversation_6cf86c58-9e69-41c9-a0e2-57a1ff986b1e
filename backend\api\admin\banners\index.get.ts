/**
 * 管理员获取横幅列表接口
 * GET /api/admin/banners
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法管理横幅'
    //   });
    // }

    // 获取查询参数
    const queryParams = getQuery(event);
    const { page, pageSize, offset } = validatePagination(queryParams.page, queryParams.pageSize);
    const status = validateStatus(queryParams.status as string);
    const search = queryParams.search as string;

    // 构建查询条件
    let whereClause = 'WHERE 1=1';
    const params: any[] = [];

    // 搜索功能：支持Banner标题和副标题的模糊搜索
    if (search) {
      whereClause += ' AND (title LIKE ? OR subtitle LIKE ?)';
      const searchPattern = `%${search}%`;
      params.push(searchPattern, searchPattern);
    }

    if (status !== null) {
      whereClause += ' AND is_active = ?';
      params.push(status);
    }

    // 获取总数
    const countResult = await query(
      `SELECT COUNT(*) as total FROM banners ${whereClause}`,
      params
    );
    const total = countResult[0]?.total || 0;

    // 获取横幅列表
    const banners = await query(
      `SELECT id, title, subtitle, image_url, link_url, background_color, 
              text_color, is_active, open_in_new_tab, sort_order, created_at, updated_at
       FROM banners 
       ${whereClause}
       ORDER BY sort_order ASC, id ASC
       LIMIT ? OFFSET ?`,
      [...params, pageSize, offset]
    );

    // 格式化横幅数据
    const formattedBanners = banners.map((banner: any) => ({
      id: banner.id,
      title: banner.title,
      subtitle: banner.subtitle,
      imageUrl: banner.image_url,
      linkUrl: banner.link_url,
      backgroundColor: banner.background_color,
      textColor: banner.text_color,
      isActive: banner.is_active === 1,
      openInNewTab: banner.open_in_new_tab === 1,
      sortOrder: banner.sort_order,
      createdAt: banner.created_at,
      updatedAt: banner.updated_at
    }));

    return {
      success: true,
      data: {
        list: formattedBanners,
        pagination: {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize)
        }
      }
    };

  } catch (error: any) {
    logger.error('获取横幅列表失败', {
      error: error.message,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    // 如果是已知错误，直接抛出
    if (error.statusCode) {
      throw error;
    }

    // 未知错误
    throw createError({
      statusCode: 500,
      statusMessage: '服务器内部错误'
    });
  }
});
