import { query } from '~/utils/database';
import { logger, logAdminAction, getClientIP } from '~/utils/logger';
import { checkAdminPermission, PERMISSIONS } from '~/utils/permission';

/**
 * 管理员发布新闻接口
 * POST /api/admin/news/:id/publish
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法发布新闻'
    //   });
    // }

    // 获取新闻ID
    const newsId = getRouterParam(event, 'id');
    if (!newsId || isNaN(parseInt(newsId))) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的新闻ID'
      });
    }

    const newsIdNum = parseInt(newsId);

    // 获取请求体
    const body = await readBody(event);
    const { 
      action = 'publish', // publish, unpublish, archive
      publish_date 
    } = body;

    // 验证操作类型
    const validActions = ['publish', 'unpublish', 'archive'];
    if (!validActions.includes(action)) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的操作类型'
      });
    }

    // 检查新闻是否存在
    const existingNews = await query(
      'SELECT id, title, status, content FROM news WHERE id = ?',
      [newsIdNum]
    );

    if (!existingNews || existingNews.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: '新闻不存在'
      });
    }

    const news = existingNews[0];

    // 验证新闻内容完整性（发布时）
    if (action === 'publish') {
      if (!news.title || !news.content) {
        throw createError({
          statusCode: 400,
          statusMessage: '新闻标题和内容不能为空'
        });
      }
    }

    // 处理发布时间
    let publishDateTime = null;
    let newStatus = '';

    switch (action) {
      case 'publish':
        newStatus = 'published';
        if (publish_date) {
          publishDateTime = new Date(publish_date);
          if (isNaN(publishDateTime.getTime())) {
            throw createError({
              statusCode: 400,
              statusMessage: '无效的发布时间'
            });
          }
        } else {
          publishDateTime = new Date();
        }
        break;
      
      case 'unpublish':
        newStatus = 'draft';
        publishDateTime = null;
        break;
      
      case 'archive':
        newStatus = 'archived';
        // 保持原有发布时间
        break;
    }

    // 更新新闻状态
    const updateQuery = action === 'archive' 
      ? 'UPDATE news SET status = ?, updated_at = NOW() WHERE id = ?'
      : 'UPDATE news SET status = ?, publish_date = ?, updated_at = NOW() WHERE id = ?';
    
    const updateParams = action === 'archive' 
      ? [newStatus, newsIdNum]
      : [newStatus, publishDateTime, newsIdNum];

    await query(updateQuery, updateParams);

    // 构建响应消息
    let message = '';
    switch (action) {
      case 'publish':
        message = publishDateTime > new Date() ? '新闻定时发布设置成功' : '新闻发布成功';
        break;
      case 'unpublish':
        message = '新闻已下线';
        break;
      case 'archive':
        message = '新闻已归档';
        break;
    }

    // 记录审计日志
    await logAdminAction(admin.id, `news:${action}`, message, {
      newsId: newsIdNum,
      title: news.title,
      oldStatus: news.status,
      newStatus,
      publishDate: publishDateTime
    });

    return {
      success: true,
      message,
      data: {
        id: newsIdNum,
        title: news.title,
        status: newStatus,
        publishDate: publishDateTime
      }
    };

  } catch (error: any) {
    logger.error('新闻状态更新失败', {
      error: error.message,
      stack: error.stack,
      newsId: getRouterParam(event, 'id'),
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: '新闻状态更新失败'
    });
  }
});
