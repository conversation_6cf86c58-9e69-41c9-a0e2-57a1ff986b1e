import { query } from '~/utils/database';
import { logger, logAdminAction, getClientIP } from '~/utils/logger';
import { checkAdminPermission, PERMISSIONS } from '~/utils/permission';

/**
 * 管理员获取新闻统计数据接口
 * GET /api/admin/news/stats
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法查看新闻统计'
    //   });
    // }

    // 获取总新闻数量
    const totalNewsQuery = `
      SELECT COUNT(*) as total
      FROM news
    `;
    const totalNewsResult = await query(totalNewsQuery);
    const totalNews = totalNewsResult[0]?.total || 0;

    // 获取已发布新闻数量
    const publishedNewsQuery = `
      SELECT COUNT(*) as total
      FROM news
      WHERE status = 'published'
    `;
    const publishedNewsResult = await query(publishedNewsQuery);
    const publishedNews = publishedNewsResult[0]?.total || 0;

    // 获取草稿新闻数量
    const draftNewsQuery = `
      SELECT COUNT(*) as total
      FROM news
      WHERE status = 'draft'
    `;
    const draftNewsResult = await query(draftNewsQuery);
    const draftNews = draftNewsResult[0]?.total || 0;

    // 获取精选新闻数量
    const featuredNewsQuery = `
      SELECT COUNT(*) as total
      FROM news
      WHERE is_featured = 1
    `;
    const featuredNewsResult = await query(featuredNewsQuery);
    const featuredNews = featuredNewsResult[0]?.total || 0;

    // 获取总阅读量
    const totalViewsQuery = `
      SELECT SUM(view_count) as total
      FROM news
      WHERE status = 'published'
    `;
    const totalViewsResult = await query(totalViewsQuery);
    const totalViews = totalViewsResult[0]?.total || 0;

    // 获取今日新增新闻数量
    const todayNewsQuery = `
      SELECT COUNT(*) as total
      FROM news
      WHERE DATE(created_at) = CURDATE()
    `;
    const todayNewsResult = await query(todayNewsQuery);
    const todayNews = todayNewsResult[0]?.total || 0;

    // 获取本周新增新闻数量
    const weekNewsQuery = `
      SELECT COUNT(*) as total
      FROM news
      WHERE YEARWEEK(created_at, 1) = YEARWEEK(CURDATE(), 1)
    `;
    const weekNewsResult = await query(weekNewsQuery);
    const weekNews = weekNewsResult[0]?.total || 0;

    // 获取本月新增新闻数量
    const monthNewsQuery = `
      SELECT COUNT(*) as total
      FROM news
      WHERE YEAR(created_at) = YEAR(CURDATE()) 
        AND MONTH(created_at) = MONTH(CURDATE())
    `;
    const monthNewsResult = await query(monthNewsQuery);
    const monthNews = monthNewsResult[0]?.total || 0;

    // 获取分类统计
    const categoryStatsQuery = `
      SELECT 
        nc.id,
        nc.name,
        COUNT(n.id) as news_count
      FROM news_categories nc
      LEFT JOIN news n ON nc.id = n.category_id
      GROUP BY nc.id, nc.name
      ORDER BY news_count DESC
      LIMIT 10
    `;
    const categoryStats = await query(categoryStatsQuery);

    // 获取最近7天的新闻发布趋势
    const trendQuery = `
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as count
      FROM news
      WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
      GROUP BY DATE(created_at)
      ORDER BY date ASC
    `;
    const trendData = await query(trendQuery);

    const stats = {
      overview: {
        totalNews,
        publishedNews,
        draftNews,
        featuredNews,
        totalViews
      },
      period: {
        today: todayNews,
        week: weekNews,
        month: monthNews
      },
      categories: categoryStats.map(cat => ({
        id: cat.id,
        name: cat.name,
        newsCount: cat.news_count
      })),
      trend: trendData.map(item => ({
        date: item.date,
        count: item.count
      }))
    };

    // 记录审计日志
    await logAdminAction(admin.id, 'news:stats', '查看新闻统计数据', {});

    return {
      success: true,
      data: stats
    };

  } catch (error: any) {
    logger.error('获取新闻统计数据失败', {
      error: error.message,
      stack: error.stack,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: '获取新闻统计数据失败'
    });
  }
});
