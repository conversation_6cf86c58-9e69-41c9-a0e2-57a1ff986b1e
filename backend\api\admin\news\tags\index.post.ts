import { query } from '~/utils/database';
import { logger, logAdminAction, getClientIP } from '~/utils/logger';
import { checkAdminPermission, PERMISSIONS } from '~/utils/permission';

/**
 * 管理员创建新闻标签接口
 * POST /api/admin/news/tags
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法创建标签'
    //   });
    // }

    // 获取请求体
    const body = await readBody(event);
    const { name } = body;

    // 验证必填字段
    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: '标签名称不能为空'
      });
    }

    const trimmedName = name.trim();

    // 验证标签名称长度
    if (trimmedName.length > 50) {
      throw createError({
        statusCode: 400,
        statusMessage: '标签名称不能超过50个字符'
      });
    }

    // 检查标签是否已存在
    const existingTag = await query(
      'SELECT id FROM news_tags WHERE name = ?',
      [trimmedName]
    );

    if (existingTag.length > 0) {
      throw createError({
        statusCode: 400,
        statusMessage: '标签名称已存在'
      });
    }

    // 创建标签
    const insertResult = await query(
      `INSERT INTO news_tags (name, created_at, updated_at) 
       VALUES (?, NOW(), NOW())`,
      [trimmedName]
    );

    const tagId = insertResult.insertId;

    // 查询创建的标签信息
    const newTag = await query(
      'SELECT id, name, created_at, updated_at FROM news_tags WHERE id = ?',
      [tagId]
    );

    const tag = newTag[0];

    // 记录审计日志
    await logAdminAction(admin.id, 'news:tags:create', '创建新闻标签', {
      tagId,
      tagName: trimmedName
    });

    return {
      success: true,
      message: '标签创建成功',
      data: {
        id: tag.id,
        name: tag.name,
        usageCount: 0,
        createdAt: tag.created_at,
        updatedAt: tag.updated_at
      }
    };

  } catch (error: any) {
    logger.error('创建标签失败', {
      error: error.message,
      stack: error.stack,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: '创建标签失败'
    });
  }
});
