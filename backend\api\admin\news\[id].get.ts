import { query } from '~/utils/database';
import { logger, logAdminAction, getClientIP } from '~/utils/logger';
import { checkAdminPermission, PERMISSIONS } from '~/utils/permission';

/**
 * 管理员获取新闻详情接口
 * GET /api/admin/news/:id
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法查看新闻详情'
    //   });
    // }

    // 获取新闻ID
    const newsId = getRouterParam(event, 'id');
    if (!newsId || isNaN(parseInt(newsId))) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的新闻ID'
      });
    }

    const newsIdNum = parseInt(newsId);

    // 查询新闻详情
    const newsQuery = `
      SELECT 
        n.id,
        n.title,
        n.summary,
        n.content,
        n.cover_image_url,
        n.author,
        n.source_url,
        n.status,
        n.view_count,
        n.is_featured,
        n.publish_date,
        n.created_at,
        n.updated_at,
        nc.id as category_id,
        nc.name as category_name,
        nc.slug as category_slug,
        nc.description as category_description
      FROM news n
      LEFT JOIN news_categories nc ON n.category_id = nc.id
      WHERE n.id = ?
    `;

    const newsResult = await query(newsQuery, [newsIdNum]);

    if (!newsResult || newsResult.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: '新闻不存在'
      });
    }

    const news = newsResult[0];

    // 查询新闻标签
    const tagsQuery = `
      SELECT nt.id, nt.name
      FROM news_tags nt
      INNER JOIN news_tag_relations ntr ON nt.id = ntr.tag_id
      WHERE ntr.news_id = ?
      ORDER BY nt.name
    `;

    const tags = await query(tagsQuery, [newsIdNum]);

    // 格式化响应数据
    const formattedNews = {
      id: news.id,
      title: news.title,
      summary: news.summary,
      content: news.content,
      coverImage: news.cover_image_url,
      author: news.author,
      sourceUrl: news.source_url,
      status: news.status,
      viewCount: news.view_count,
      isFeatured: Boolean(news.is_featured),
      publishDate: news.publish_date,
      createdAt: news.created_at,
      updatedAt: news.updated_at,
      category: news.category_id ? {
        id: news.category_id,
        name: news.category_name,
        slug: news.category_slug,
        description: news.category_description
      } : null,
      tags: tags.map(tag => ({
        id: tag.id,
        name: tag.name
      }))
    };

    // 记录审计日志
    await logAdminAction(admin.id, 'news:view', '查看新闻详情', {
      newsId: newsIdNum,
      title: news.title
    });

    return {
      success: true,
      data: formattedNews
    };

  } catch (error: any) {
    logger.error('获取新闻详情失败', {
      error: error.message,
      stack: error.stack,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: '获取新闻详情失败'
    });
  }
});
