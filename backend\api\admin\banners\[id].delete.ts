/**
 * 管理员删除横幅接口
 * DELETE /api/admin/banners/[id]
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法删除横幅'
    //   });
    // }

    // 获取横幅ID
    const bannerId = getRouterParam(event, 'id');
    if (!bannerId || isNaN(Number(bannerId))) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的横幅ID'
      });
    }

    // 检查横幅是否存在
    const existingBanner = await query(
      'SELECT * FROM banners WHERE id = ?',
      [bannerId]
    );

    if (existingBanner.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: '横幅不存在'
      });
    }

    const banner = existingBanner[0];

    // 删除横幅
    await query(
      'DELETE FROM banners WHERE id = ?',
      [bannerId]
    );

    // 记录审计日志
    await logAuditAction({
      action: 'ADMIN_DELETE_BANNER',
      description: `管理员删除横幅: ${banner.title}`,
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, 'user-agent') || '',
      details: {
        bannerId: Number(bannerId),
        deletedData: banner
      }
    });

    logger.info('管理员删除横幅成功', {
      adminId: admin.id,
      adminUsername: admin.username,
      bannerId: Number(bannerId),
      title: banner.title
    });

    return {
      success: true,
      message: '横幅删除成功',
      data: {
        id: Number(bannerId)
      }
    };

  } catch (error: any) {
    logger.error('删除横幅失败', {
      error: error.message,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    // 如果是已知错误，直接抛出
    if (error.statusCode) {
      throw error;
    }

    // 未知错误
    throw createError({
      statusCode: 500,
      statusMessage: '服务器内部错误'
    });
  }
});
