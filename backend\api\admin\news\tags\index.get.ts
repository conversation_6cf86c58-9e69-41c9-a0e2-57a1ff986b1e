import { query } from '~/utils/database';
import { logger, logAdminAction, getClientIP } from '~/utils/logger';
import { checkAdminPermission, PERMISSIONS } from '~/utils/permission';

/**
 * 管理员获取新闻标签列表接口
 * GET /api/admin/news/tags
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法查看标签列表'
    //   });
    // }

    // 获取查询参数
    const query_params = getQuery(event);
    const { 
      page = 1, 
      pageSize = 20, 
      search
    } = query_params;

    // 验证分页参数
    const pageNum = Math.max(1, parseInt(page as string) || 1);
    const pageSizeNum = Math.min(100, Math.max(1, parseInt(pageSize as string) || 20));
    const offset = (pageNum - 1) * pageSizeNum;

    // 构建查询条件
    let whereConditions = [];
    let queryParams = [];

    if (search && typeof search === 'string' && search.trim()) {
      whereConditions.push('nt.name LIKE ?');
      queryParams.push(`%${search.trim()}%`);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // 查询标签总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM news_tags nt
      ${whereClause}
    `;

    const countResult = await query(countQuery, queryParams);
    const total = countResult[0]?.total || 0;
    const totalPages = Math.ceil(total / pageSizeNum);

    // 查询标签列表
    const tagsQuery = `
      SELECT 
        nt.id,
        nt.name,
        nt.created_at,
        nt.updated_at,
        COUNT(ntr.news_id) as usage_count
      FROM news_tags nt
      LEFT JOIN news_tag_relations ntr ON nt.id = ntr.tag_id
      ${whereClause}
      GROUP BY nt.id, nt.name, nt.created_at, nt.updated_at
      ORDER BY usage_count DESC, nt.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const tags = await query(tagsQuery, [...queryParams, pageSizeNum, offset]);

    // 格式化标签数据
    const formattedTags = tags.map((tag: any) => ({
      id: tag.id,
      name: tag.name,
      usageCount: tag.usage_count || 0,
      createdAt: tag.created_at,
      updatedAt: tag.updated_at
    }));

    // 记录审计日志
    await logAdminAction(admin.id, 'news:tags:list', '查看标签列表', {
      filters: { search },
      pagination: { page: pageNum, pageSize: pageSizeNum }
    });

    return {
      success: true,
      data: {
        list: formattedTags,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total,
          totalPages
        }
      }
    };

  } catch (error: any) {
    logger.error('获取标签列表失败', {
      error: error.message,
      stack: error.stack,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: '获取标签列表失败'
    });
  }
});
