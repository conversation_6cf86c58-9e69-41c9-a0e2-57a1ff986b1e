import { query, transaction } from '~/utils/database';
import { logger, logAdminAction, getClientIP } from '~/utils/logger';
import { checkAdminPermission, PERMISSIONS } from '~/utils/permission';

/**
 * 管理员批量操作新闻接口
 * POST /api/admin/news/batch-operation
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法执行批量操作'
    //   });
    // }

    // 获取请求体
    const body = await readBody(event);
    const { ids, action } = body;

    // 验证参数
    if (!Array.isArray(ids) || ids.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: '请选择要操作的新闻'
      });
    }

    if (!action || typeof action !== 'string') {
      throw createError({
        statusCode: 400,
        statusMessage: '请指定操作类型'
      });
    }

    const validActions = ['delete', 'publish', 'unpublish', 'archive', 'feature', 'unfeature'];
    if (!validActions.includes(action)) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的操作类型'
      });
    }

    // 验证新闻ID
    const newsIds = ids.filter(id => !isNaN(parseInt(id))).map(id => parseInt(id));
    if (newsIds.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的新闻ID'
      });
    }

    // 检查新闻是否存在
    const existingNews = await query(
      `SELECT id, title FROM news WHERE id IN (${newsIds.map(() => '?').join(',')})`,
      newsIds
    );

    if (existingNews.length !== newsIds.length) {
      throw createError({
        statusCode: 400,
        statusMessage: '部分新闻不存在'
      });
    }

    let updateQuery = '';
    let updateParams: any[] = [];
    let actionDescription = '';

    // 根据操作类型构建更新语句
    switch (action) {
      case 'delete':
        // 删除操作需要使用事务
        await transaction(async (connection) => {
          // 删除新闻标签关联
          await connection.execute(
            `DELETE FROM news_tag_relations WHERE news_id IN (${newsIds.map(() => '?').join(',')})`,
            newsIds
          );

          // 删除新闻阅读记录
          await connection.execute(
            `DELETE FROM news_read_logs WHERE news_id IN (${newsIds.map(() => '?').join(',')})`,
            newsIds
          );

          // 删除新闻SEO信息
          await connection.execute(
            `DELETE FROM news_seo WHERE news_id IN (${newsIds.map(() => '?').join(',')})`,
            newsIds
          );

          // 删除新闻主记录
          await connection.execute(
            `DELETE FROM news WHERE id IN (${newsIds.map(() => '?').join(',')})`,
            newsIds
          );
        });

        actionDescription = '批量删除新闻';
        break;

      case 'publish':
        updateQuery = `
          UPDATE news 
          SET status = 'published', 
              publish_date = COALESCE(publish_date, NOW()),
              updated_at = NOW()
          WHERE id IN (${newsIds.map(() => '?').join(',')})
        `;
        updateParams = newsIds;
        actionDescription = '批量发布新闻';
        break;

      case 'unpublish':
        updateQuery = `
          UPDATE news 
          SET status = 'draft', 
              publish_date = NULL,
              updated_at = NOW()
          WHERE id IN (${newsIds.map(() => '?').join(',')})
        `;
        updateParams = newsIds;
        actionDescription = '批量下线新闻';
        break;

      case 'archive':
        updateQuery = `
          UPDATE news 
          SET status = 'archived',
              updated_at = NOW()
          WHERE id IN (${newsIds.map(() => '?').join(',')})
        `;
        updateParams = newsIds;
        actionDescription = '批量归档新闻';
        break;

      case 'feature':
        updateQuery = `
          UPDATE news 
          SET is_featured = 1,
              updated_at = NOW()
          WHERE id IN (${newsIds.map(() => '?').join(',')})
        `;
        updateParams = newsIds;
        actionDescription = '批量设为推荐';
        break;

      case 'unfeature':
        updateQuery = `
          UPDATE news 
          SET is_featured = 0,
              updated_at = NOW()
          WHERE id IN (${newsIds.map(() => '?').join(',')})
        `;
        updateParams = newsIds;
        actionDescription = '批量取消推荐';
        break;
    }

    // 执行更新操作（除了删除操作）
    if (updateQuery) {
      await query(updateQuery, updateParams);
    }

    // 记录审计日志
    await logAdminAction(admin.id, 'news:batch_operation', actionDescription, {
      action,
      newsIds,
      newsCount: newsIds.length,
      newsTitles: existingNews.map((news: any) => news.title)
    });

    return {
      success: true,
      message: `${actionDescription}成功`,
      data: {
        action,
        affectedCount: newsIds.length,
        newsIds
      }
    };

  } catch (error: any) {
    logger.error('批量操作新闻失败', {
      error: error.message,
      stack: error.stack,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: '批量操作失败'
    });
  }
});
