/**
 * 管理员更新横幅接口
 * PUT /api/admin/banners/[id]
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法更新横幅'
    //   });
    // }

    // 获取横幅ID
    const bannerId = getRouterParam(event, 'id');
    if (!bannerId || isNaN(Number(bannerId))) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的横幅ID'
      });
    }

    // 检查横幅是否存在
    const existingBanner = await query(
      'SELECT * FROM banners WHERE id = ?',
      [bannerId]
    );

    if (existingBanner.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: '横幅不存在'
      });
    }

    // 获取请求体
    const body = await readBody(event);
    const { 
      title, 
      subtitle, 
      image_url, 
      link_url, 
      background_color, 
      text_color, 
      is_active, 
      open_in_new_tab,
      sort_order 
    } = body;

    // 验证必填字段
    if (!title) {
      throw createError({
        statusCode: 400,
        statusMessage: '横幅标题不能为空'
      });
    }

    if (!image_url) {
      throw createError({
        statusCode: 400,
        statusMessage: '横幅图片不能为空'
      });
    }

    // 处理布尔值
    const isActiveValue = is_active === true || is_active === 1 || is_active === '1' ? 1 : 0;
    const openInNewTabValue = open_in_new_tab === true || open_in_new_tab === 1 || open_in_new_tab === '1' ? 1 : 0;

    // 处理排序值
    let finalSortOrder = sort_order;
    if (finalSortOrder === undefined || finalSortOrder === null || isNaN(Number(finalSortOrder))) {
      finalSortOrder = 0;
    }

    // 更新横幅
    await query(
      `UPDATE banners 
       SET title = ?, subtitle = ?, image_url = ?, link_url = ?, background_color = ?, 
           text_color = ?, is_active = ?, open_in_new_tab = ?, sort_order = ?, updated_at = NOW()
       WHERE id = ?`,
      [
        title.trim(),
        subtitle?.trim() || null,
        image_url.trim(),
        link_url?.trim() || null,
        background_color || '#ffffff',
        text_color || '#000000',
        isActiveValue,
        openInNewTabValue,
        finalSortOrder,
        bannerId
      ]
    );

    // 记录审计日志
    await logAuditAction({
      action: 'ADMIN_UPDATE_BANNER',
      description: `管理员更新横幅: ${title}`,
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, 'user-agent') || '',
      details: {
        bannerId: Number(bannerId),
        title,
        subtitle,
        imageUrl: image_url,
        linkUrl: link_url,
        isActive: isActiveValue === 1,
        sortOrder: finalSortOrder,
        oldData: existingBanner[0]
      }
    });

    logger.info('管理员更新横幅成功', {
      adminId: admin.id,
      adminUsername: admin.username,
      bannerId: Number(bannerId),
      title
    });

    return {
      success: true,
      message: '横幅更新成功',
      data: {
        id: Number(bannerId)
      }
    };

  } catch (error: any) {
    logger.error('更新横幅失败', {
      error: error.message,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    // 如果是已知错误，直接抛出
    if (error.statusCode) {
      throw error;
    }

    // 未知错误
    throw createError({
      statusCode: 500,
      statusMessage: '服务器内部错误'
    });
  }
});
