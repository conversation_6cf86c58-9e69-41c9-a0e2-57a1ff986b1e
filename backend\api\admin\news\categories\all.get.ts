import { query } from '~/utils/database';
import { logger, getClientIP } from '~/utils/logger';
import { checkAdminPermission, PERMISSIONS } from '~/utils/permission';

/**
 * 管理员获取所有新闻分类接口（用于下拉选择）
 * GET /api/admin/news/categories/all
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 查询所有启用的分类
    const categoriesQuery = `
      SELECT 
        id,
        name,
        slug,
        description,
        parent_id,
        sort_order
      FROM news_categories
      WHERE is_active = 1
      ORDER BY sort_order ASC, id ASC
    `;

    const categories = await query(categoriesQuery);

    // 格式化分类数据
    const formattedCategories = categories.map((category: any) => ({
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      parentId: category.parent_id,
      sortOrder: category.sort_order
    }));

    return {
      success: true,
      data: formattedCategories
    };

  } catch (error: any) {
    logger.error('获取所有新闻分类失败', {
      error: error.message,
      stack: error.stack,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: '获取分类列表失败'
    });
  }
});
