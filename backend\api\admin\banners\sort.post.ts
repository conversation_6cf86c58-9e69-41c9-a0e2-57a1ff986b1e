/**
 * 管理员更新横幅排序接口
 * POST /api/admin/banners/sort
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法更新横幅排序'
    //   });
    // }

    // 获取请求体
    const body = await readBody(event);
    const { orders } = body;

    // 验证参数
    if (!orders || !Array.isArray(orders) || orders.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: '排序数据不能为空'
      });
    }

    // 验证排序数据格式
    for (const order of orders) {
      if (!order.id || isNaN(Number(order.id)) || isNaN(Number(order.sortOrder))) {
        throw createError({
          statusCode: 400,
          statusMessage: '排序数据格式错误'
        });
      }
    }

    // 开始事务
    const connection = await getConnection();
    await connection.beginTransaction();

    try {
      // 批量更新排序
      for (const order of orders) {
        await connection.execute(
          'UPDATE banners SET sort_order = ?, updated_at = NOW() WHERE id = ?',
          [Number(order.sortOrder), Number(order.id)]
        );
      }

      await connection.commit();

      // 记录审计日志
      await logAuditAction({
        action: 'ADMIN_UPDATE_BANNER_SORT',
        description: `管理员更新横幅排序`,
        userId: admin.id,
        username: admin.username,
        ip: getClientIP(event),
        userAgent: getHeader(event, 'user-agent') || '',
        details: {
          orders: orders.map(o => ({ id: Number(o.id), sortOrder: Number(o.sortOrder) }))
        }
      });

      logger.info('管理员更新横幅排序成功', {
        adminId: admin.id,
        adminUsername: admin.username,
        updateCount: orders.length
      });

      return {
        success: true,
        message: '横幅排序更新成功',
        data: {
          updateCount: orders.length
        }
      };

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }

  } catch (error: any) {
    logger.error('更新横幅排序失败', {
      error: error.message,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    // 如果是已知错误，直接抛出
    if (error.statusCode) {
      throw error;
    }

    // 未知错误
    throw createError({
      statusCode: 500,
      statusMessage: '服务器内部错误'
    });
  }
});
