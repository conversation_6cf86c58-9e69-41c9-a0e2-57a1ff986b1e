/**
 * 管理员创建横幅接口
 * POST /api/admin/banners
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法创建横幅'
    //   });
    // }

    // 获取请求体
    const body = await readBody(event);
    const { 
      title, 
      subtitle, 
      image_url, 
      link_url, 
      background_color, 
      text_color, 
      is_active, 
      open_in_new_tab,
      sort_order 
    } = body;

    // 验证必填字段
    if (!title) {
      throw createError({
        statusCode: 400,
        statusMessage: '横幅标题不能为空'
      });
    }

    if (!image_url) {
      throw createError({
        statusCode: 400,
        statusMessage: '横幅图片不能为空'
      });
    }

    // 验证数据类型
    const isActiveValue = is_active ? 1 : 0;
    const openInNewTabValue = open_in_new_tab ? 1 : 0;
    const sortOrderValue = sort_order ? parseInt(sort_order) : 0;

    // 如果没有指定排序，获取下一个排序值
    let finalSortOrder = sortOrderValue;
    if (finalSortOrder === 0) {
      const maxSortResult = await query('SELECT MAX(sort_order) as max_sort FROM banners');
      finalSortOrder = (maxSortResult[0]?.max_sort || 0) + 1;
    }

    // 插入新横幅
    const result = await query(
      `INSERT INTO banners (title, subtitle, image_url, link_url, background_color, 
                           text_color, is_active, open_in_new_tab, sort_order, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [
        title.trim(),
        subtitle?.trim() || null,
        image_url.trim(),
        link_url?.trim() || null,
        background_color || '#ffffff',
        text_color || '#000000',
        isActiveValue,
        openInNewTabValue,
        finalSortOrder
      ]
    );

    const newBannerId = (result as any).insertId;

    // 记录审计日志
    await logAuditAction({
      action: 'ADMIN_CREATE_BANNER',
      description: `管理员创建横幅: ${title}`,
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, 'user-agent') || '',
      details: {
        bannerId: newBannerId,
        title,
        subtitle,
        imageUrl: image_url,
        linkUrl: link_url,
        isActive: isActiveValue === 1,
        sortOrder: finalSortOrder
      }
    });

    logger.info('管理员创建横幅成功', {
      adminId: admin.id,
      adminUsername: admin.username,
      bannerId: newBannerId,
      title
    });

    return {
      success: true,
      message: '横幅创建成功',
      data: {
        id: newBannerId
      }
    };

  } catch (error: any) {
    logger.error('创建横幅失败', {
      error: error.message,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    // 如果是已知错误，直接抛出
    if (error.statusCode) {
      throw error;
    }

    // 未知错误
    throw createError({
      statusCode: 500,
      statusMessage: '服务器内部错误'
    });
  }
});
