/**
 * 管理员批量删除横幅接口
 * POST /api/admin/banners/batch-delete
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法删除横幅'
    //   });
    // }

    // 获取请求体
    const body = await readBody(event);
    const { ids } = body;

    // 验证参数
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: '请选择要删除的横幅'
      });
    }

    // 验证所有ID都是数字
    const validIds = ids.filter(id => !isNaN(Number(id))).map(id => Number(id));
    if (validIds.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的横幅ID'
      });
    }

    // 查询要删除的横幅信息（用于日志记录）
    const existingBanners = await query(
      `SELECT id, title FROM banners WHERE id IN (${validIds.map(() => '?').join(',')})`,
      validIds
    );

    if (existingBanners.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: '未找到要删除的横幅'
      });
    }

    // 批量删除横幅
    const deleteResult = await query(
      `DELETE FROM banners WHERE id IN (${validIds.map(() => '?').join(',')})`,
      validIds
    );

    const deletedCount = (deleteResult as any).affectedRows || 0;

    // 记录审计日志
    await logAuditAction({
      action: 'ADMIN_BATCH_DELETE_BANNERS',
      description: `管理员批量删除横幅: ${existingBanners.map(b => b.title).join(', ')}`,
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, 'user-agent') || '',
      details: {
        deletedIds: validIds,
        deletedCount,
        deletedBanners: existingBanners
      }
    });

    logger.info('管理员批量删除横幅成功', {
      adminId: admin.id,
      adminUsername: admin.username,
      deletedIds: validIds,
      deletedCount
    });

    return {
      success: true,
      message: `成功删除 ${deletedCount} 个横幅`,
      data: {
        deletedCount,
        deletedIds: validIds
      }
    };

  } catch (error: any) {
    logger.error('批量删除横幅失败', {
      error: error.message,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    // 如果是已知错误，直接抛出
    if (error.statusCode) {
      throw error;
    }

    // 未知错误
    throw createError({
      statusCode: 500,
      statusMessage: '服务器内部错误'
    });
  }
});
