import { query, transaction } from '~/utils/database';
import { logger, logAdminAction, getClientIP } from '~/utils/logger';
import { checkAdminPermission, PERMISSIONS } from '~/utils/permission';

/**
 * 管理员删除新闻接口
 * DELETE /api/admin/news/:id
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法删除新闻'
    //   });
    // }

    // 获取新闻ID
    const newsId = getRouterParam(event, 'id');
    if (!newsId || isNaN(parseInt(newsId))) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的新闻ID'
      });
    }

    const newsIdNum = parseInt(newsId);

    // 检查新闻是否存在
    const existingNews = await query(
      'SELECT id, title, status, cover_image_url FROM news WHERE id = ?',
      [newsIdNum]
    );

    if (!existingNews || existingNews.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: '新闻不存在'
      });
    }

    const news = existingNews[0];

    // 使用事务删除新闻及相关数据
    await transaction(async (connection) => {
      // 删除新闻标签关联
      await connection.execute(
        'DELETE FROM news_tag_relations WHERE news_id = ?',
        [newsIdNum]
      );

      // 删除新闻阅读记录
      await connection.execute(
        'DELETE FROM news_read_logs WHERE news_id = ?',
        [newsIdNum]
      );

      // 删除新闻SEO信息
      await connection.execute(
        'DELETE FROM news_seo WHERE news_id = ?',
        [newsIdNum]
      );

      // 删除新闻主记录
      await connection.execute(
        'DELETE FROM news WHERE id = ?',
        [newsIdNum]
      );
    });

    // 记录审计日志
    await logAdminAction(admin.id, 'news:delete', '删除新闻', {
      newsId: newsIdNum,
      title: news.title,
      status: news.status
    });

    return {
      success: true,
      message: '新闻删除成功',
      data: {
        id: newsIdNum,
        title: news.title
      }
    };

  } catch (error: any) {
    logger.error('删除新闻失败', {
      error: error.message,
      stack: error.stack,
      newsId: getRouterParam(event, 'id'),
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: '删除新闻失败'
    });
  }
});
