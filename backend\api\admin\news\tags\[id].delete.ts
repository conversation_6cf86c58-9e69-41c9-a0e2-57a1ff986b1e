import { query, transaction } from '~/utils/database';
import { logger, logAdminAction, getClientIP } from '~/utils/logger';
import { checkAdminPermission, PERMISSIONS } from '~/utils/permission';

/**
 * 管理员删除新闻标签接口
 * DELETE /api/admin/news/tags/:id
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法删除标签'
    //   });
    // }

    // 获取标签ID
    const tagId = getRouterParam(event, 'id');
    if (!tagId || isNaN(parseInt(tagId))) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的标签ID'
      });
    }

    const tagIdNum = parseInt(tagId);

    // 检查标签是否存在
    const existingTag = await query(
      'SELECT id, name FROM news_tags WHERE id = ?',
      [tagIdNum]
    );

    if (!existingTag || existingTag.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: '标签不存在'
      });
    }

    const tag = existingTag[0];

    // 检查标签是否被使用
    const usageCheck = await query(
      'SELECT COUNT(*) as count FROM news_tag_relations WHERE tag_id = ?',
      [tagIdNum]
    );

    const usageCount = usageCheck[0]?.count || 0;

    // 使用事务删除标签和相关关联
    await transaction(async (connection) => {
      // 删除标签与新闻的关联关系
      if (usageCount > 0) {
        await connection.execute(
          'DELETE FROM news_tag_relations WHERE tag_id = ?',
          [tagIdNum]
        );
      }

      // 删除标签
      await connection.execute(
        'DELETE FROM news_tags WHERE id = ?',
        [tagIdNum]
      );
    });

    // 记录审计日志
    await logAdminAction(admin.id, 'news:tags:delete', '删除新闻标签', {
      tagId: tagIdNum,
      tagName: tag.name,
      usageCount
    });

    return {
      success: true,
      message: `标签"${tag.name}"删除成功${usageCount > 0 ? `，同时移除了${usageCount}个关联关系` : ''}`
    };

  } catch (error: any) {
    logger.error('删除标签失败', {
      error: error.message,
      stack: error.stack,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: '删除标签失败'
    });
  }
});
