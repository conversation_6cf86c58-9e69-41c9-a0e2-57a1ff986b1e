import { query, transaction } from '~/utils/database';
import { logger, logAdminAction, getClientIP } from '~/utils/logger';
import { checkAdminPermission, PERMISSIONS } from '~/utils/permission';

/**
 * 管理员创建新闻接口
 * POST /api/admin/news
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法创建新闻'
    //   });
    // }

    // 获取请求体
    const body = await readBody(event);
    const { 
      title, 
      summary, 
      content, 
      cover_image_url, 
      category_id, 
      author, 
      source_url, 
      status = 'draft',
      is_featured = false,
      publish_date,
      tags = [],
      seo
    } = body;

    // 验证必填字段
    if (!title || typeof title !== 'string' || title.trim().length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: '新闻标题不能为空'
      });
    }

    if (!content || typeof content !== 'string' || content.trim().length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: '新闻内容不能为空'
      });
    }

    // 验证状态值
    const validStatuses = ['draft', 'pending', 'published', 'archived'];
    if (!validStatuses.includes(status)) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的新闻状态'
      });
    }

    // 验证分类ID（如果提供）
    if (category_id && !isNaN(parseInt(category_id))) {
      const categoryCheck = await query(
        'SELECT id FROM news_categories WHERE id = ? AND is_active = 1',
        [parseInt(category_id)]
      );
      
      if (!categoryCheck || categoryCheck.length === 0) {
        throw createError({
          statusCode: 400,
          statusMessage: '无效的新闻分类'
        });
      }
    }

    // 处理发布时间
    let publishDateTime = null;
    if (publish_date) {
      publishDateTime = new Date(publish_date);
      if (isNaN(publishDateTime.getTime())) {
        throw createError({
          statusCode: 400,
          statusMessage: '无效的发布时间'
        });
      }
    } else if (status === 'published') {
      publishDateTime = new Date();
    }

    // 使用事务创建新闻
    const newsId = await transaction(async (connection) => {
      // 插入新闻主记录
      const insertNewsQuery = `
        INSERT INTO news (
          title, summary, content, cover_image_url, category_id, 
          author, source_url, status, is_featured, publish_date,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `;

      const newsResult = await connection.execute(insertNewsQuery, [
        title.trim(),
        summary ? summary.trim() : null,
        content.trim(),
        cover_image_url || null,
        category_id ? parseInt(category_id) : null,
        author ? author.trim() : null,
        source_url || null,
        status,
        is_featured ? 1 : 0,
        publishDateTime
      ]);

      const newNewsId = (newsResult as any).insertId;

      // 处理标签关联
      if (tags && Array.isArray(tags) && tags.length > 0) {
        for (const tagName of tags) {
          if (typeof tagName === 'string' && tagName.trim()) {
            // 查找或创建标签
            let tagId;
            const existingTag = await connection.execute(
              'SELECT id FROM news_tags WHERE name = ?',
              [tagName.trim()]
            );

            if ((existingTag as any)[0].length > 0) {
              tagId = (existingTag as any)[0][0].id;
            } else {
              const tagResult = await connection.execute(
                'INSERT INTO news_tags (name) VALUES (?)',
                [tagName.trim()]
              );
              tagId = (tagResult as any).insertId;
            }

            // 创建新闻标签关联
            await connection.execute(
              'INSERT INTO news_tag_relations (news_id, tag_id) VALUES (?, ?)',
              [newNewsId, tagId]
            );
          }
        }
      }

      // 处理SEO信息
      if (seo && typeof seo === 'object') {
        const {
          meta_title,
          meta_description,
          meta_keywords,
          canonical_url,
          og_title,
          og_description,
          og_image
        } = seo;

        await connection.execute(
          `INSERT INTO news_seo (
            news_id, meta_title, meta_description, meta_keywords,
            canonical_url, og_title, og_description, og_image
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            newNewsId,
            meta_title || null,
            meta_description || null,
            meta_keywords || null,
            canonical_url || null,
            og_title || null,
            og_description || null,
            og_image || null
          ]
        );
      }

      return newNewsId;
    });

    // 记录审计日志
    await logAdminAction(admin.id, 'news:create', '创建新闻', {
      newsId,
      title: title.trim(),
      status,
      categoryId: category_id
    });

    return {
      success: true,
      message: '新闻创建成功',
      data: {
        id: newsId,
        title: title.trim(),
        status
      }
    };

  } catch (error: any) {
    logger.error('创建新闻失败', {
      error: error.message,
      stack: error.stack,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: '创建新闻失败'
    });
  }
});
